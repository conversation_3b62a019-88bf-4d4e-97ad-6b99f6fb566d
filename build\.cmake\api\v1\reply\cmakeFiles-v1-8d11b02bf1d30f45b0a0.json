{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake"}], "kind": "cmakeFiles", "paths": {"build": "E:/workspace/vscode/NodeNetwork/build", "source": "E:/workspace/vscode/NodeNetwork"}, "version": {"major": 1, "minor": 1}}