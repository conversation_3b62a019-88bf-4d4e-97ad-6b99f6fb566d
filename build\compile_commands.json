[{"directory": "E:/workspace/vscode/NodeNetwork/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe   -g -std=gnu++17 -o CMakeFiles\\NodeNetwork.dir\\NodeNetwork.cpp.obj -c E:\\workspace\\vscode\\NodeNetwork\\NodeNetwork.cpp", "file": "E:/workspace/vscode/NodeNetwork/NodeNetwork.cpp", "output": "CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj"}, {"directory": "E:/workspace/vscode/NodeNetwork/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe   -g -std=gnu++17 -o CMakeFiles\\NodeNetwork.dir\\main.cpp.obj -c E:\\workspace\\vscode\\NodeNetwork\\main.cpp", "file": "E:/workspace/vscode/NodeNetwork/main.cpp", "output": "CMakeFiles/NodeNetwork.dir/main.cpp.obj"}]