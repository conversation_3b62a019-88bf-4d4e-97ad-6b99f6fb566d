# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\NodeNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\NodeNetwork\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/NodeNetwork.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/NodeNetwork.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/NodeNetwork.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/NodeNetwork.dir

# All Build rule for target.
CMakeFiles/NodeNetwork.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\NodeNetwork.dir\build.make CMakeFiles/NodeNetwork.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\NodeNetwork.dir\build.make CMakeFiles/NodeNetwork.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\NodeNetwork\build\CMakeFiles --progress-num=1,2,3 "Built target NodeNetwork"
.PHONY : CMakeFiles/NodeNetwork.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NodeNetwork.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\NodeNetwork\build\CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/NodeNetwork.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\NodeNetwork\build\CMakeFiles 0
.PHONY : CMakeFiles/NodeNetwork.dir/rule

# Convenience name for target.
NodeNetwork: CMakeFiles/NodeNetwork.dir/rule
.PHONY : NodeNetwork

# codegen rule for target.
CMakeFiles/NodeNetwork.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\NodeNetwork.dir\build.make CMakeFiles/NodeNetwork.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\NodeNetwork\build\CMakeFiles --progress-num=1,2,3 "Finished codegen for target NodeNetwork"
.PHONY : CMakeFiles/NodeNetwork.dir/codegen

# clean rule for target.
CMakeFiles/NodeNetwork.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\NodeNetwork.dir\build.make CMakeFiles/NodeNetwork.dir/clean
.PHONY : CMakeFiles/NodeNetwork.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

