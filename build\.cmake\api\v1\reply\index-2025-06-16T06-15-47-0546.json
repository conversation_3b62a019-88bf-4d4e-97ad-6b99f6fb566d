{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "D:/msys64/mingw64/bin/cmake.exe", "cpack": "D:/msys64/mingw64/bin/cpack.exe", "ctest": "D:/msys64/mingw64/bin/ctest.exe", "root": "D:/msys64/mingw64/share/cmake"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 2, "string": "4.0.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-e5c9287a6994f1a2a592.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-f25d51cc4c02d19ba13d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-8d11b02bf1d30f45b0a0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-b89b3de09f892a592718.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-f25d51cc4c02d19ba13d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-e5c9287a6994f1a2a592.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-b89b3de09f892a592718.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-8d11b02bf1d30f45b0a0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}