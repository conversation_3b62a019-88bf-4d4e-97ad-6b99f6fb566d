# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\NodeNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\NodeNetwork\build

# Include any dependencies generated for this target.
include CMakeFiles/NodeNetwork.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/NodeNetwork.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/NodeNetwork.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/NodeNetwork.dir/flags.make

CMakeFiles/NodeNetwork.dir/codegen:
.PHONY : CMakeFiles/NodeNetwork.dir/codegen

CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj: CMakeFiles/NodeNetwork.dir/flags.make
CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj: E:/workspace/vscode/NodeNetwork/NodeNetwork.cpp
CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj: CMakeFiles/NodeNetwork.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\NodeNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj -MF CMakeFiles\NodeNetwork.dir\NodeNetwork.cpp.obj.d -o CMakeFiles\NodeNetwork.dir\NodeNetwork.cpp.obj -c E:\workspace\vscode\NodeNetwork\NodeNetwork.cpp

CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\NodeNetwork\NodeNetwork.cpp > CMakeFiles\NodeNetwork.dir\NodeNetwork.cpp.i

CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\NodeNetwork\NodeNetwork.cpp -o CMakeFiles\NodeNetwork.dir\NodeNetwork.cpp.s

CMakeFiles/NodeNetwork.dir/main.cpp.obj: CMakeFiles/NodeNetwork.dir/flags.make
CMakeFiles/NodeNetwork.dir/main.cpp.obj: E:/workspace/vscode/NodeNetwork/main.cpp
CMakeFiles/NodeNetwork.dir/main.cpp.obj: CMakeFiles/NodeNetwork.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\NodeNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/NodeNetwork.dir/main.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/NodeNetwork.dir/main.cpp.obj -MF CMakeFiles\NodeNetwork.dir\main.cpp.obj.d -o CMakeFiles\NodeNetwork.dir\main.cpp.obj -c E:\workspace\vscode\NodeNetwork\main.cpp

CMakeFiles/NodeNetwork.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/NodeNetwork.dir/main.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\NodeNetwork\main.cpp > CMakeFiles\NodeNetwork.dir\main.cpp.i

CMakeFiles/NodeNetwork.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/NodeNetwork.dir/main.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\NodeNetwork\main.cpp -o CMakeFiles\NodeNetwork.dir\main.cpp.s

# Object files for target NodeNetwork
NodeNetwork_OBJECTS = \
"CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj" \
"CMakeFiles/NodeNetwork.dir/main.cpp.obj"

# External object files for target NodeNetwork
NodeNetwork_EXTERNAL_OBJECTS =

NodeNetwork.exe: CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj
NodeNetwork.exe: CMakeFiles/NodeNetwork.dir/main.cpp.obj
NodeNetwork.exe: CMakeFiles/NodeNetwork.dir/build.make
NodeNetwork.exe: CMakeFiles/NodeNetwork.dir/linkLibs.rsp
NodeNetwork.exe: CMakeFiles/NodeNetwork.dir/objects1.rsp
NodeNetwork.exe: CMakeFiles/NodeNetwork.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\NodeNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable NodeNetwork.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\NodeNetwork.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/NodeNetwork.dir/build: NodeNetwork.exe
.PHONY : CMakeFiles/NodeNetwork.dir/build

CMakeFiles/NodeNetwork.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\NodeNetwork.dir\cmake_clean.cmake
.PHONY : CMakeFiles/NodeNetwork.dir/clean

CMakeFiles/NodeNetwork.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\NodeNetwork E:\workspace\vscode\NodeNetwork E:\workspace\vscode\NodeNetwork\build E:\workspace\vscode\NodeNetwork\build E:\workspace\vscode\NodeNetwork\build\CMakeFiles\NodeNetwork.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/NodeNetwork.dir/depend

