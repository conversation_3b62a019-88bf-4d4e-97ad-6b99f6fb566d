#ifndef NODENETWORK_H
#define NODENETWORK_H

#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <string>

class NodeNetwork
{
public:
    explicit NodeNetwork();
    void addNodeVariables(const std::string& node,
                          const std::vector<std::string>& variables);
    std::vector<std::string> getNodeVariables(const std::string& node) const;
    std::vector<std::string> getNodes() const;
    void addNodeVariable(const std::string& node,
                         const std::string& variable);
    void setVariableState(const std::string& variable,
                          bool state);
    std::vector<std::vector<std::string>> getFinalConnectedPaths(const std::string& startNode,
                                                           int maxPathLength);
    std::vector<std::string> getTerminalNodes(const std::string& startNode,
                                        int maxPathLength);
    std::vector<std::string> getAllNodes(const std::string& startNode,
                                   int maxPathLength = 1000);
    std::vector<std::string> getAllNodesWithActiveVariables(const std::string& startNode,
                                                            int maxPathLength = 1000);
    std::vector<std::string> getNonSharedVariables(const std::string& node);
    std::unordered_map<std::string, std::vector<std::string>> getTerminalNodesWithPrivateVariables(const std::string& startNode,
                                                                                                     int maxPathLength);
    std::vector<std::string> getCommonVariables(const std::string& node1,
                                          const std::string& node2);

    std::unordered_map<std::string, std::vector<std::string>> getAllCommonVariables(const std::string& node);
    std::vector<std::string> getNodeNeighbors(const std::string& node) const;
    std::vector<std::string> getNodeNeighbors(const std::string& node,
                                        const std::string& variable) const;
    std::vector<std::string> getNodesByVariable(const std::string& variable) const;

private:
    void dfs(const std::string& currentNode,
             std::unordered_set<std::string>& visited,
             std::vector<std::string>& currentPath,
             std::vector<std::vector<std::string>>& finalPaths,
             size_t maxPathLength);
    void dfsCollectReachableNodes(const std::string& currentNode,
                                  std::unordered_set<std::string>& visited,
                                  std::unordered_set<std::string>& reachableNodes,
                                  size_t maxPathLength);
    void dfsCollectReachableNodesWithActiveVariables(const std::string& currentNode,
                                                     std::unordered_set<std::string>& visited,
                                                     std::unordered_set<std::string>& reachableNodes,
                                                     size_t maxPathLength);
    void updateCommonVariables(const std::string& node,
                               const std::string& var);

private:
    std::unordered_map<std::string, std::vector<std::string>> nodes_; // 节点到变量点的映射
    std::unordered_map<std::string, std::unordered_set<std::string>> variableToNodes_; // 变量点到节点的映射
    std::unordered_set<std::string> activeVariables_; // 激活的变量点集合
    std::unordered_map<std::string, std::unordered_map<std::string, std::vector<std::string>>> commonVariables_; // 公共变量点信息
};

#endif // NODENETWORK_H
