#ifndef NODENETWORK_H
#define NODENETWORK_H

#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <string>

class NodeNetwork
{
public:
    using StringList = std::vector<std::string>;
    using PathList = std::vector<StringList>;
    using NodeVariablesMap = std::unordered_map<std::string, StringList>;

    explicit NodeNetwork();

    void addNodeVariables(const std::string& node,
                          const StringList& variables);
    StringList getNodeVariables(const std::string& node) const;
    StringList getNodes() const;
    void addNodeVariable(const std::string& node,
                         const std::string& variable);
    void setVariableState(const std::string& variable,
                          bool state);
    PathList getFinalConnectedPaths(const std::string& startNode,
                                    int maxPathLength);
    StringList getTerminalNodes(const std::string& startNode,
                                int maxPathLength);
    NodeVariablesMap getTerminalNodesWithPrivateVariables(const std::string& startNode,
                                                                  int maxPathLength);
    StringList getAllNodes(const std::string& startNode,
                           int maxPathLength = 1000);
    StringList getAllActiveNodes(const std::string& startNode,
                                 int maxPathLength = 1000);
    StringList getPrivateVariables(const std::string& node);

    StringList getCommonVariables(const std::string& node1,
                                  const std::string& node2);

    NodeVariablesMap getCommonVariables(const std::string& node);
    StringList getNeighbors(const std::string& node) const;
    StringList getNeighbors(const std::string& node,
                            const std::string& variable) const;
    StringList getVariableNodes(const std::string& variable) const;

private:
    using StringSet = std::unordered_set<std::string>;
    using VariableNodesMap = std::unordered_map<std::string, StringSet>;
    using AllCommonVariablesMap = std::unordered_map<std::string, NodeVariablesMap>;

    void dfs(const std::string& currentNode,
             StringSet& visited,
             StringList& currentPath,
             PathList& finalPaths,
             size_t maxPathLength);
    void dfsCollectReachableNodes(const std::string& currentNode,
                                  StringSet& visited,
                                  StringSet& reachableNodes,
                                  size_t maxPathLength,
                                  bool onlyActiveVariables = false);
    void updateCommonVariables(const std::string& node,
                               const std::string& var);

    NodeVariablesMap nodes_; // 节点到变量点的映射
    VariableNodesMap variableToNodes_; // 变量点到节点的映射
    StringSet activeVariables_; // 激活的变量点集合
    AllCommonVariablesMap commonVariables_; // 公共变量点信息
};

#endif // NODENETWORK_H
