#include "NodeNetwork.h"
#include <iostream>
#include <vector>
#include <string>

void printVector(const std::vector<std::string>& vec, const std::string& label) {
    std::cout << label << ": ";
    for (size_t i = 0; i < vec.size(); ++i) {
        std::cout << vec[i];
        if (i < vec.size() - 1) std::cout << ", ";
    }
    std::cout << std::endl;
}

void testGetFinalEndpoints() {
    std::cout << "=== Testing getFinalEndpoints Function ===" << std::endl;

    NodeNetwork network;

    // Create test network structure:
    // Node1 -- var1 -- Node2 -- var2 -- Node3
    //   |                |               |
    // var3             var4            var5
    //   |                |               |
    // Node4 -- var6 -- Node5 -- var7 -- Node6

    // Add nodes and variables
    network.addNodeVariables("Node1", {"var1", "var3"});
    network.addNodeVariables("Node2", {"var1", "var2", "var4"});
    network.addNodeVariables("Node3", {"var2", "var5"});
    network.addNodeVariables("Node4", {"var3", "var6"});
    network.addNodeVariables("Node5", {"var4", "var6", "var7"});
    network.addNodeVariables("Node6", {"var5", "var7"});

    std::cout << "\nNetwork structure created:" << std::endl;
    std::cout << "Node1 -- var1 -- Node2 -- var2 -- Node3" << std::endl;
    std::cout << "  |                |               |" << std::endl;
    std::cout << "var3             var4            var5" << std::endl;
    std::cout << "  |                |               |" << std::endl;
    std::cout << "Node4 -- var6 -- Node5 -- var7 -- Node6" << std::endl;

    // Test 1: All variables inactive
    std::cout << "\n--- Test 1: All variables inactive ---" << std::endl;
    auto endpoints1 = network.getFinalEndpoints("Node1", 10);
    printVector(endpoints1, "Endpoints from Node1");
    std::cout << "Expected: Should find all physically adjacent terminal nodes (Node1, Node3, Node4, Node6)" << std::endl;

    // Test 2: Only activate some variables
    std::cout << "\n--- Test 2: Only activate var1 and var2 ---" << std::endl;
    network.setVariableState("var1", true);
    network.setVariableState("var2", true);
    auto endpoints2 = network.getFinalEndpoints("Node1", 10);
    printVector(endpoints2, "Endpoints from Node1");
    std::cout << "Expected: Should still find all physically adjacent terminal nodes, not limited by variable activation" << std::endl;

    // Test 3: Activate all variables
    std::cout << "\n--- Test 3: Activate all variables ---" << std::endl;
    network.setVariableState("var3", true);
    network.setVariableState("var4", true);
    network.setVariableState("var5", true);
    network.setVariableState("var6", true);
    network.setVariableState("var7", true);
    auto endpoints3 = network.getFinalEndpoints("Node1", 10);
    printVector(endpoints3, "Endpoints from Node1");
    std::cout << "Expected: Results should be same as previous tests" << std::endl;

    // Test 4: Different starting node
    std::cout << "\n--- Test 4: Starting from Node2 ---" << std::endl;
    auto endpoints4 = network.getFinalEndpoints("Node2", 10);
    printVector(endpoints4, "Endpoints from Node2");
    std::cout << "Expected: Should find Node1, Node3, Node4, Node6" << std::endl;

    // Test 5: Limit path length
    std::cout << "\n--- Test 5: Limit path length to 2 ---" << std::endl;
    auto endpoints5 = network.getFinalEndpoints("Node1", 2);
    printVector(endpoints5, "Endpoints from Node1 (max path length 2)");
    std::cout << "Expected: Due to path length limit, may only reach some nodes" << std::endl;
}

void testGetFinalConnectedPaths() {
    std::cout << "\n=== Testing getFinalConnectedPaths Function ===" << std::endl;

    NodeNetwork network;

    // Create simple linear network: Node1 -- var1 -- Node2 -- var2 -- Node3
    network.addNodeVariables("Node1", {"var1"});
    network.addNodeVariables("Node2", {"var1", "var2"});
    network.addNodeVariables("Node3", {"var2"});

    std::cout << "\nSimple linear network: Node1 -- var1 -- Node2 -- var2 -- Node3" << std::endl;

    // Test 1: Variables inactive
    std::cout << "\n--- Test 1: Variables inactive ---" << std::endl;
    auto paths1 = network.getFinalConnectedPaths("Node1", 10);
    std::cout << "Number of paths from Node1: " << paths1.size() << std::endl;
    for (size_t i = 0; i < paths1.size(); ++i) {
        std::cout << "Path " << (i+1) << ": ";
        for (size_t j = 0; j < paths1[i].size(); ++j) {
            std::cout << paths1[i][j];
            if (j < paths1[i].size() - 1) std::cout << " -> ";
        }
        std::cout << std::endl;
    }
    std::cout << "Expected: Should traverse entire network, not limited by variable activation" << std::endl;

    // Test 2: Activate variables
    std::cout << "\n--- Test 2: Activate all variables ---" << std::endl;
    network.setVariableState("var1", true);
    network.setVariableState("var2", true);
    auto paths2 = network.getFinalConnectedPaths("Node1", 10);
    std::cout << "Number of paths from Node1: " << paths2.size() << std::endl;
    for (size_t i = 0; i < paths2.size(); ++i) {
        std::cout << "Path " << (i+1) << ": ";
        for (size_t j = 0; j < paths2[i].size(); ++j) {
            std::cout << paths2[i][j];
            if (j < paths2[i].size() - 1) std::cout << " -> ";
        }
        std::cout << std::endl;
    }
    std::cout << "Expected: Results should be same as previous test" << std::endl;
}

void testSimpleEndpoints() {
    std::cout << "\n=== Simple Endpoints Test ===" << std::endl;

    NodeNetwork network;

    // Create simple network: Node1 -- var1 -- Node2
    network.addNodeVariables("Node1", {"var1"});
    network.addNodeVariables("Node2", {"var1"});

    std::cout << "Simple network: Node1 -- var1 -- Node2" << std::endl;

    // Test without activation
    std::cout << "\n--- Without variable activation ---" << std::endl;
    auto endpoints1 = network.getFinalEndpoints("Node1", 10);
    printVector(endpoints1, "Endpoints from Node1");

    // Test with activation
    std::cout << "\n--- With variable activation ---" << std::endl;
    network.setVariableState("var1", true);
    auto endpoints2 = network.getFinalEndpoints("Node1", 10);
    printVector(endpoints2, "Endpoints from Node1");

    std::cout << "Expected: Both tests should find Node1 and Node2 as endpoints (degree <= 1)" << std::endl;
}

int main() {
    std::cout << "NodeNetwork Function Test" << std::endl;
    std::cout << "Testing modified getFinalEndpoints and getFinalConnectedPaths methods" << std::endl;
    std::cout << "Verifying they now consider all physical adjacency relationships, not just active variables" << std::endl;

    testGetFinalEndpoints();
    testGetFinalConnectedPaths();
    testSimpleEndpoints();

    std::cout << "\n=== Test Complete ===" << std::endl;
    return 0;
}
